import { useEffect, type ReactNode } from "react";
import { isRouteErrorResponse, useNavigate, useRouteError } from "react-router";
import type { LinksFunction } from "react-router";

import { HtmlBody } from "./HtmlBody";
import { LayoutStandalone } from "./LayoutStandalone";

// Constants
const DEFAULT_ERROR_MESSAGE = "Something went wrong.";
const MESSAGES: Record<number, string> = {
  401: "You are not logged in.",
  403: "You do not have access to this page.",
  404: "This page does not exist.",
  500: DEFAULT_ERROR_MESSAGE,
  502: DEFAULT_ERROR_MESSAGE,
  503: DEFAULT_ERROR_MESSAGE,
  504: DEFAULT_ERROR_MESSAGE,
};

// Fragments
const ErrorBoundaryBase = ({
  title,
  heading,
  children,
}: {
  title: string;
  heading?: string;
  children: ReactNode;
}) => (
  <HtmlBody>
    <LayoutStandalone title={heading ?? title}>{children}</LayoutStandalone>
  </HtmlBody>
);

// Exports
export const links: LinksFunction = () => [];

export const ErrorBoundary = () => {
  const error = useRouteError();
  const navigate = useNavigate();
  // eslint-disable-next-line no-console
  console.error(error);

  useEffect(() => {
    if (isRouteErrorResponse(error) && error.status === 401) {
      navigate("/auth/logout");
    }
  }, [error, navigate]);

  // Handle network (4xx/5xx) errors
  if (isRouteErrorResponse(error)) {
    return (
      <ErrorBoundaryBase title={`${error.status} ${error.statusText}`}>
        <p>{MESSAGES[error.status] ?? DEFAULT_ERROR_MESSAGE}</p>
      </ErrorBoundaryBase>
    );
  }

  // Handle application errors
  if (error instanceof Error) {
    return (
      <ErrorBoundaryBase title="Unknown error">
        <p>{error.message}</p>
      </ErrorBoundaryBase>
    );
  }

  // Handle non-Error exceptions
  return (
    <ErrorBoundaryBase title="Unknown error">
      <p>{DEFAULT_ERROR_MESSAGE}</p>
    </ErrorBoundaryBase>
  );
};
