import { ChevronRightOutlined } from "@mui/icons-material";
import { LinkProps, NavLink, useFetcher, useRevalidator } from "react-router";
import { formatRelative } from "date-fns";
import { toast } from "react-toastify";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/@shadcn/ui/card";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { cn } from "~/@shadcn/utils";
import { TimeStamp } from "~/@ui/TimeStamp";
import { AfterHydration } from "~/utils/hydration";

// Exports
type TaskCardProps = LinkProps & {
  uuid: string;
  completed: boolean;
  title: string;
  dueAt: string | null;
  compact?: boolean;
};
export const TaskCard = ({
  uuid,
  completed,
  title,
  dueAt,
  compact = false,
  to,
  ...linkProps
}: TaskCardProps) => {
  const { submit } = useFetcher();
  const revalidator = useRevalidator();
  return (
    <Card
      compact={compact}
      className={cn(
        "inline-flex min-w-0 grow flex-row items-center self-stretch hover:border-foreground hover:bg-accent",
        "[&.active]:border-transparent [&.active]:bg-warning-foreground/30 [&.active]:ring-2  [&.active]:ring-warning"
      )}
      // ⚠️ The whole Card is an anchor tag, so it must only contain inline
      // elements. This means no paragraphs, and all divs and buttons must be set
      // to display:inline or display:inline-flex.
      asChild
    >
      <NavLink to={to} {...linkProps}>
        <CardHeader
          title={title}
          className="grow overflow-hidden text-ellipsis whitespace-nowrap"
          asChild
        >
          <span>
            <CardTitle
              className="self-stretch overflow-hidden text-ellipsis whitespace-nowrap"
              variant={compact ? "default" : "h4"}
              asChild
            >
              <span>
                <Checkbox
                  className="top-[2px] m-0 mr-2"
                  checked={completed}
                  onClick={(e) => {
                    if (!navigator.onLine) {
                      toast.error(
                        "You are offline. Please check your connection."
                      );
                      return;
                    }
                    e.preventDefault();
                    e.stopPropagation();
                    submit(null, {
                      method: "post",
                      action: `/tasks/${uuid}/toggle-complete`,
                    });
                    revalidator.revalidate();
                  }}
                />
                {title}
              </span>
            </CardTitle>
            {!compact && (
              <CardDescription
                className="inline-flex items-center gap-3 self-stretch"
                asChild
              >
                <span>
                  <AfterHydration>
                    {dueAt ? (
                      <TimeStamp>
                        <AfterHydration>
                          Due {formatRelative(new Date(dueAt), new Date())}
                        </AfterHydration>
                      </TimeStamp>
                    ) : (
                      <TimeStamp>No due date</TimeStamp>
                    )}
                  </AfterHydration>
                </span>
              </CardDescription>
            )}
          </span>
        </CardHeader>

        <ChevronRightOutlined className="h-6 w-6" />
      </NavLink>
    </Card>
  );
};
