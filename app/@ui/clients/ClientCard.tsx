import { NavLink, LinkProps } from "react-router";
import { cn } from "~/@shadcn/utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/@shadcn/ui/card";
import { ChevronRightOutlined } from "@mui/icons-material";

// Exports
type Props = LinkProps & {
  clientName: string;
  clientType: string;
  compact?: boolean;
};
export const ClientCard = ({
  clientName,
  clientType,
  to,
  compact = false,
  ...linkProps
}: Props) => {
  return (
    <Card
      compact={compact}
      className={cn(
        "inline-flex min-w-0 flex-row items-center self-stretch hover:border-foreground hover:bg-accent",
        "[&.active]:border-warning [&.active]:bg-warning-foreground/30"
        // disabled && "pointer-events-none opacity-50 shadow-none"
      )}
      // ⚠️ The whole Card is an anchor tag, so it must only contain inline
      // elements. This means no paragraphs, and all divs and buttons must be set
      // to display:inline or display:inline-flex.
      asChild
    >
      <NavLink
        // Set tabIndex to -1 to ensure tile is not focusable in disabled state
        tabIndex={-1}
        to={to}
        {...linkProps}
      >
        <div className="flex-grow">
          <CardHeader
            title={clientName}
            className="grow overflow-hidden text-ellipsis whitespace-nowrap"
            asChild
          >
            <span>
              <CardTitle
                className="self-stretch overflow-hidden text-ellipsis whitespace-nowrap"
                variant={compact ? "default" : "h4"}
                asChild
              >
                <span>{clientName}</span>
              </CardTitle>
            </span>
          </CardHeader>

          <CardContent>
            <span className="text-sm capitalize">{clientType}</span>
          </CardContent>
        </div>

        <ChevronRightOutlined className="h-6 w-6" />
      </NavLink>
    </Card>
  );
};
