import { EditOutlined } from "@mui/icons-material";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Link } from "react-router";
import { ComponentProps } from "react";

// Exports
type Props = {
  to?: ComponentProps<typeof Link>["to"];
  tooltip?: string;
  disabled?: boolean;
  onClick?: () => void;
};

export const EditButton = ({ to, tooltip, disabled, onClick }: Props) => (
  <>
    {disabled ? (
      <Button variant="ghost" size="default" disabled={disabled}>
        Edit
        <EditOutlined />
      </Button>
    ) : (
      <Tooltip>
        <TooltipTrigger asChild>
          {to ? (
            <Button variant="ghost" size="default" asChild>
              <Link to={to}>
                Edit
                <EditOutlined />
              </Link>
            </Button>
          ) : (
            <Button variant="ghost" size="default" onClick={onClick}>
              Edit
              <EditOutlined />
            </Button>
          )}
        </TooltipTrigger>
        <TooltipContent>{tooltip ?? "Edit"}</TooltipContent>
      </Tooltip>
    )}
  </>
);
