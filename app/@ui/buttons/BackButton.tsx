import { ArrowBackOutlined } from "@mui/icons-material";
import { <PERSON> } from "react-router";
import { ComponentProps } from "react";
import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

type Props = {
  className?: string;
  to: ComponentProps<typeof Link>["to"];
  tooltip: string;
};
export const BackButton = ({ className, to, tooltip }: Props) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button className={className} size="icon-sm" variant="outline" asChild>
        <Link to={to}>
          <ArrowBackOutlined />
        </Link>
      </Button>
    </TooltipTrigger>
    <TooltipContent side="right">{tooltip}</TooltipContent>
  </Tooltip>
);
