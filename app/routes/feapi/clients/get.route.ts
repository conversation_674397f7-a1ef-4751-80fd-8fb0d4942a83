// API URL: /feapi/clients/get
import { data, LoaderFunctionArgs } from "react-router";
import { getClients } from "~/api/notes/getClients.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const searchTerm = url.searchParams.get("searchTerm") || "";
  const pageSize = parseInt(url.searchParams.get("pageSize") || "0");
  const cursor = url.searchParams.get("cursor") || "";

  const { clients, nextPageToken } = await getClients({
    searchTerm,
    request,
    pageSize,
    cursor,
  });
  return data({ clients, nextPageToken });
};
