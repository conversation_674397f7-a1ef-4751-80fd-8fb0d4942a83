import { ContentCopyOutlined } from "@mui/icons-material";
import { useState } from "react";
import { toast } from "react-toastify";
import { Mail, NotebookPen } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { FollowUp } from "~/api/openapi/generated";
import {
  followUpDataForFollowUp,
  UnstructuredTextData,
} from "~/routes/_auth.notes.$id._index/followUpDataTypes";
import MarkdownEditor from "~/@ui/MarkdownEditor";
import { Link, useLocation } from "react-router";

type ButtonSize = "icon-lg" | "icon" | "icon-sm" | "default";

export const OpenMeetingPrepButton = ({
  to,
  title,
  variant = "default",
  id,
}: {
  to: { pathname: string; search?: string };
  title?: string;
  variant?: "ghost" | "default";
  id?: string;
}) => {
  const location = useLocation();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant={variant} id={id} asChild>
          <Link
            to={to}
            state={{ from: `${location.pathname}${location.search}` }}
          >
            <NotebookPen className="!h-5 !w-5" />
            <span className={variant !== "default" ? "hidden sm:block" : ""}>
              {title}
            </span>
          </Link>
        </Button>
      </TooltipTrigger>
      <TooltipContent>Go to meeting prep</TooltipContent>
    </Tooltip>
  );
};

export const StartMeetingButton = ({
  to,
}: {
  to: { pathname: string; search?: string };
}) => {
  const location = useLocation();
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="default" size="default" asChild>
          <Link
            to={to}
            state={{ from: `${location.pathname}${location.search}` }}
          >
            Start meeting
          </Link>
        </Button>
      </TooltipTrigger>
      <TooltipContent>Go to the meeting start page</TooltipContent>
    </Tooltip>
  );
};

const AgendaDialog = ({
  isOpen,
  closeDialog,
  unstrucuredTextData,
  subject,
  handleCopy,
}: {
  isOpen: boolean;
  closeDialog: () => void;
  unstrucuredTextData: UnstructuredTextData;
  subject: string;
  handleCopy: () => void;
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent className="flex max-h-[95vh] w-full flex-col p-4 sm:max-w-5xl">
        <DialogHeader className="flex-shrink-0">
          <div className="flex w-full items-center justify-between">
            <DialogTitle>Agenda preview</DialogTitle>
          </div>
        </DialogHeader>

        <MarkdownEditor
          markdown={unstrucuredTextData.content}
          onChange={() => {}}
          readOnly={true}
          showToolbar={false}
        />

        <DialogFooter className="mt-4 flex-shrink-0">
          <Button onClick={handleCopy} className="flex items-center gap-2">
            <ContentCopyOutlined />
            Copy Agenda
          </Button>
          <Button onClick={closeDialog} variant="ghost">
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const GenerateAgendaEmailButton = ({
  emailSubject,
  agenda,
  size = "icon-sm",
  disabled = false,
  className,
  title,
}: {
  emailSubject: string;
  agenda: FollowUp | null;
  size?: ButtonSize;
  className?: string;
  disabled?: boolean;
  title?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  let results = followUpDataForFollowUp(agenda);
  let parsedData = results?.parsedData as UnstructuredTextData | undefined;
  let isUnstructuredText = parsedData ? "content" in parsedData : false;

  const handleCopy = () => {
    if (!isUnstructuredText) {
      return;
    }
    navigator.clipboard.writeText(parsedData?.content ?? "");
    toast.success("Agenda copied to clipboard!", { autoClose: 2000 });
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            className={className}
            onClick={() => setIsOpen(true)}
            disabled={!isUnstructuredText || disabled}
          >
            <Mail /> {title && <span className="hidden sm:block">{title}</span>}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          {isUnstructuredText
            ? "Generate agenda email"
            : "Can not generate agenda email"}
        </TooltipContent>
      </Tooltip>
      {isUnstructuredText && parsedData && (
        <AgendaDialog
          isOpen={isOpen}
          closeDialog={() => setIsOpen(false)}
          subject={emailSubject}
          unstrucuredTextData={parsedData}
          handleCopy={handleCopy}
        />
      )}
    </>
  );
};
