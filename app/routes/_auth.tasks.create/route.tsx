import { useState } from "react";
import { AccessTimeOutlined, EventNoteOutlined } from "@mui/icons-material";
import {
  data,
  redirect,
  type ActionFunctionArgs,
  type LoaderFunctionArgs,
  type MetaFunction,
} from "react-router";
import {
  Form,
  unstable_usePrompt,
  useActionData,
  useLoaderData,
} from "react-router";
import { format } from "date-fns";
import { z } from "zod";
import { flattenZodErrors, formDataToObject } from "~/utils/validation";
import { AfterHydration } from "~/utils/hydration";
import { TaskDueAtCard } from "~/@ui/tasks/TaskDueAtCard";
import { logError } from "~/utils/log.server";
import { TaskEditableTitle } from "~/@ui/tasks/TaskEditableTitle";
import { getNotes } from "~/api/notes/getNotes.server";
import { BackButton } from "~/@ui/buttons/BackButton";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { SaveButton } from "~/@ui/buttons/SaveButton";
import { Typography } from "~/@ui/Typography";
import { Separator } from "~/@shadcn/ui/separator";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { FormControl, FormField, FormLabel } from "~/@shadcn/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { Textarea } from "~/@shadcn/ui/textarea";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";

// Types
const CreateTaskFormStruct = z.object({
  details: z.string().optional(),
  dueAt: z.string().datetime().nullable().optional(),
  title: z.string().min(1, "Missing 'title' field"),
  parentNoteUuid: z.string().nullable(),
});

// Fragments
const SidebarHeader = ({
  disabled,
  formId,
}: {
  disabled?: boolean;
  formId: string;
}) => (
  <HeaderV2
    subtitle="Create task"
    left={<BackButton to="/tasks" tooltip="Back to tasks" />}
    right={
      <div className="flex flex-row gap-2">
        <SaveButton disabled={disabled} tooltip="Save task" formId={formId} />
      </div>
    }
  />
);

// Exports
export const meta: MetaFunction = () => {
  return [
    { title: "Create task" },
    { name: "description", content: "Create task" },
  ];
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Validate form data
    const validatedFormData = CreateTaskFormStruct.parse(
      formDataToObject(await request.formData())
    );

    // Client sends us "---" as a placeholder for "nothing selected", but the
    // Django backend expects a `null`
    if (validatedFormData.parentNoteUuid === "---") {
      validatedFormData.parentNoteUuid = null;
    }
    const { userId } = await getUserSessionOrRedirect(request);
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const { taskUuid } = await new TaskApi(configuration).taskCreateTask({
      createTaskRequest: {
        title: validatedFormData.title,
        ownerUuid: userId,
        description: validatedFormData.details,
        dueDate: validatedFormData.dueAt
          ? new Date(validatedFormData.dueAt)
          : null,
        parentNoteUuid: validatedFormData.parentNoteUuid,
      },
    });

    return redirect(`/tasks/${taskUuid}`);
  } catch (error) {
    logError("app/routes/tasks.create.tsx action error", error);

    // Form validation errors
    if (error instanceof z.ZodError) {
      return data({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    return data({ errors: ["Failed to save task"] }, { status: 500 });
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const notes = await getNotes({ request });
  const payload = { notes };
  return data(payload);
};

const Route = () => {
  const { notes } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const formErrors = useFormErrors(actionData);

  // Track if user has modified form fields. If they have, warn then that
  // navigating away will discard unsaved changes.
  const [touched, setTouched] = useState(false);
  unstable_usePrompt({
    message: "Discard unsaved changes?",
    when: ({ currentLocation, nextLocation }) => {
      return touched && currentLocation.pathname !== nextLocation.pathname;
    },
  });

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={<SidebarHeader formId="createTaskForm" />}
    >
      <Form
        id="createTaskForm"
        className="flex h-full flex-col items-center self-stretch"
        method="post"
      >
        <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
          <FormAlertsStack errors={formErrors} />
          <TaskEditableTitle
            id="title"
            name="title"
            title=""
            onChange={() => setTouched(true)}
          />
          <AfterHydration>
            <Typography className="inline-flex items-center rounded-md">
              <AccessTimeOutlined className="h-6 w-6" />
              <span className="mx-1 grow">Created</span>
              <Typography asChild color="secondary" variant="body2">
                <span>{format(new Date(), "ccc, MMM do, h:mm aaa")}</span>
              </Typography>
            </Typography>
          </AfterHydration>
          <Separator className="my-2" />
          <FormField id="parentNoteUuid" name="parentNoteUuid">
            <FormLabel>Parent note</FormLabel>
            <Select
              name="parentNoteUuid"
              defaultValue="---"
              onValueChange={() => setTouched(true)}
            >
              <FormControl>
                <SelectTrigger leftIcon={<EventNoteOutlined />}>
                  <SelectValue />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="---">---</SelectItem>
                {notes.map((note) => (
                  <SelectItem key={note.uuid} value={note.uuid}>
                    {note.meetingName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <AfterHydration>
            <TaskDueAtCard
              dueAt={null}
              onAccept={(nextVal) => {
                if (nextVal) setTouched(true);
              }}
            />
          </AfterHydration>
          <FormField id="details" name="details">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea
                id="details"
                resizable
                variant="outline"
                placeholder="Add description (optional)"
                rows={5}
                onChange={() => setTouched(true)}
              />
            </FormControl>
          </FormField>
        </div>
      </Form>
    </SidebarV2>
  );
};
export default Route;
