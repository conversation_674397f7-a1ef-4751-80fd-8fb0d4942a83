import { AuthorizationError } from "remix-auth";
import {
  data,
  LoaderFunction,
  type ActionFunctionArgs,
  type MetaFunction,
} from "react-router";
import {
  EmailOutlined,
  LockOutlined,
  VisibilityOffOutlined,
  VisibilityOutlined,
} from "@mui/icons-material";
import { z } from "zod";
import {
  Form,
  useActionData,
  useLoaderData,
  useSubmit,
  Link as RouterLink,
} from "react-router";
import { LayoutStandalone } from "~/@ui/layout/LayoutStandalone";
import {
  EMAIL_PASSWORD_STRATEGY,
  authenticator,
} from "~/auth/authenticator.server";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { MicrosoftButton } from "~/@ui/auth/MicrosoftButton";
import { GoogleButton } from "~/@ui/auth/GoogleButton";
import { useEffect, useState } from "react";
import { RedirectAlerts } from "./RedirectAlerts";
import { Link } from "~/@ui/Link";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { Typography } from "~/@ui/Typography";
import { Divider } from "~/@ui/Divider";
import { flattenZodErrors } from "~/utils/validation";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
} from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import { Spinner } from "~/@ui/assets/Spinner";

type LoaderData = {
  autoRedirect: boolean;
};

// Exports
export const meta: MetaFunction = () => [
  { title: "Login" },
  { name: "description", content: "Login " },
];

export const action = async ({ request }: ActionFunctionArgs) => {
  // Attempt to authenticate user using email strategy
  try {
    await authenticator.authenticate(EMAIL_PASSWORD_STRATEGY, request, {
      successRedirect: "/",
      throwOnError: true,
    });
  } catch (error) {
    // authenticator.authenticate redirects using thrown a Response, allow it to
    // bubble up
    if (error instanceof Response) throw error;

    // Auth service throws AuthorizationErrors
    if (error instanceof AuthorizationError) {
      if (error.cause instanceof z.ZodError) {
        return data({ errors: flattenZodErrors(error.cause) }, { status: 400 });
      }
      return data({ errors: [error.cause?.message] }, { status: 400 });
    }

    // Form validation errors
    if (error instanceof z.ZodError) {
      return data({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    // Generic errors
    let errorMessage = "Something went wrong, <NAME_EMAIL>";
    if (error instanceof Error) errorMessage = error.message;
    return data({ errors: [errorMessage] }, { status: 500 });
  }

  // authenticator.authenticate is supposed to redirect for us, so if we get to
  // this point, something went wrong
  return data({ errors: ["Login redirect failed"] }, { status: 500 });
};

export const loader: LoaderFunction = async () => {
  const autoRedirect =
    process.env.ZEPLYN_ENABLE_MICROSOFT_LOGIN_AUTO_REDIRECT === "true";
  return data<LoaderData>({ autoRedirect });
};

const Route = () => {
  const submit = useSubmit();
  const data = useActionData<typeof action>();
  const { autoRedirect } = useLoaderData<LoaderData>();
  const formErrors = useFormErrors(data);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const redirectParam = params.get("redirectTo");

    if (redirectParam) {
      localStorage.setItem("redirectTo", redirectParam);
    }

    if (autoRedirect) {
      const formData = new FormData();
      formData.append("action", "microsoftLogin");

      submit(formData, {
        action: "/auth/microsoft",
        method: "post",
      });
    }
  }, [autoRedirect, submit]);

  if (autoRedirect) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <Spinner className="h-16 w-16" />
      </div>
    );
  }

  return (
    <LayoutStandalone title="Welcome back!">
      <Divider>
        <Typography variant="body2" color="secondary">
          Use your SSO provider to log in
        </Typography>
      </Divider>
      <div className="flex justify-center gap-6 self-stretch px-0 py-6">
        <MicrosoftButton />
        <GoogleButton />
      </div>
      <Divider>
        <Typography variant="body2" color="secondary">
          Or log in with email and password
        </Typography>
      </Divider>

      <Form id="loginForm" className="flex flex-col self-stretch" method="post">
        <FormAlertsStack errors={formErrors} />
        <RedirectAlerts />
        <FormField name="email" required suppressHydrationWarning={true}>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Your email address"
              type="email"
              autoComplete="username"
              leftIcon={<EmailOutlined />}
            />
          </FormControl>
          <FormDescription className="flex justify-between">
            <span>&nbsp;</span>
          </FormDescription>
          <FormMessage />
        </FormField>

        <FormField name="password" required suppressHydrationWarning={true}>
          <FormLabel>Password</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Your password"
              type={showPassword ? "text" : "password"}
              leftIcon={<LockOutlined />}
              autoComplete="current-password"
              rightIcon={
                <Button
                  title={showPassword ? "Hide password" : "Show password"}
                  variant="ghost"
                  size="icon-sm"
                  type="button"
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    setShowPassword((prev) => !prev);
                  }}
                >
                  {showPassword ? (
                    <VisibilityOffOutlined />
                  ) : (
                    <VisibilityOutlined />
                  )}
                </Button>
              }
            />
          </FormControl>
          <FormDescription className="flex justify-end">
            <Link asChild>
              <RouterLink to="/auth/forgot-password">
                Forgot password?
              </RouterLink>
            </Link>
          </FormDescription>
          <FormMessage />
        </FormField>

        <div className="flex flex-col self-stretch px-0 py-6">
          <Button size="lg" type="submit">
            Log in
          </Button>
        </div>
      </Form>
      <Divider>
        <Typography variant="body2" color="secondary">
          Don't have an account yet?
        </Typography>
      </Divider>

      <div className="flex flex-col self-stretch px-0 py-6">
        <Link href="https://www.zeplyn.ai/start-your-journey-with-Zeplyn">
          <Button variant="outline_primary" size="lg" className="w-full">
            Sign up for a free trial
          </Button>
        </Link>
      </div>
    </LayoutStandalone>
  );
};
export default Route;
