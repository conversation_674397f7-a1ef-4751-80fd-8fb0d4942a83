import {
  redirect,
  type MetaFunction,
  data,
  type ActionFunctionArgs,
} from "react-router";
import { EmailOutlined } from "@mui/icons-material";
import { Form, Link as RouterLink, useActionData } from "react-router";
import { z } from "zod";
import { flattenZodErrors, formDataToObject } from "~/utils/validation";
import { Link } from "~/@ui/Link";
import { LayoutStandalone } from "~/@ui/layout/LayoutStandalone";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { Button } from "~/@shadcn/ui/button";
import { Typography } from "~/@ui/Typography";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
} from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import { AuthApi, Configuration } from "~/api/openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "~/api/openapi/configParams";

// Types
const ForgotPasswordFormStruct = z.object({
  email: z.string().min(1, "Email required").email("Invalid email"),
});

// Exports
export const meta: MetaFunction = () => [
  { title: "Forgot password" },
  { name: "description", content: "Forgot account passwrod" },
];

export const action = async ({ request }: ActionFunctionArgs) => {
  // Attempt to authenticate user using email strategy
  try {
    // Validate form values
    const validatedFormData = ForgotPasswordFormStruct.parse(
      formDataToObject(await request.formData())
    );

    const config = new Configuration(
      await nonAuthenticatedConfigurationParameters()
    );
    await new AuthApi(config).authForgotPassword({
      email: validatedFormData.email,
    });

    return redirect("/auth/login?forgot-password-redirect=true");
  } catch (error) {
    // Form validation errors
    if (error instanceof z.ZodError) {
      return data({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    // Generic errors
    let errorMessage = "Something went wrong";
    if (error instanceof Error) errorMessage = error.message;
    return data({ errors: [errorMessage] }, { status: 500 });
  }
};

const Route = () => {
  const data = useActionData<typeof action>();
  const formErrors = useFormErrors(data);

  return (
    <LayoutStandalone title="Forgot password">
      <Form
        id="forgotPasswordForm"
        className="flex flex-col self-stretch"
        method="post"
      >
        <FormAlertsStack errors={formErrors} />
        <FormField name="email" required>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Your email address"
              type="email"
              leftIcon={<EmailOutlined />}
            />
          </FormControl>
          <FormDescription>
            <span>&nbsp;</span>
          </FormDescription>
          <FormMessage />
        </FormField>

        <div className="flex flex-col self-stretch px-0 py-6">
          <Button type="submit" size="lg">
            Send reset instructions
          </Button>
        </div>

        <Typography variant="body2" color="secondary" className="text-center">
          Go back to{" "}
          <Link asChild>
            <RouterLink to="/auth/login">Login</RouterLink>
          </Link>
        </Typography>
      </Form>
    </LayoutStandalone>
  );
};

export default Route;
