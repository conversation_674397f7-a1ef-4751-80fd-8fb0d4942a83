import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { data } from "react-router";
import {
  vi,
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  beforeAll,
} from "vitest";

import Route, { loader } from "./route";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import { getClients } from "~/api/notes/getClients.server";
import { ClientListResponse } from "~/api/openapi/generated";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";
import { renderRouterWithWrapper } from "~/utils/testUtils";

// Mock useFetcher to control its behavior in tests
const mockFetcher = {
  data: null as any,
  state: "idle" as const,
  load: vi.fn(),
  submit: vi.fn(),
  Form: vi.fn(),
  formAction: undefined,
  formData: undefined,
  formEncType: undefined,
  formMethod: undefined,
  json: vi.fn(),
  text: vi.fn(),
};

vi.mock("react-router", async () => {
  const actual = await vi.importActual("react-router");
  return {
    ...actual,
    useFetcher: () => mockFetcher,
  };
});

vi.mock("~/utils/flagsInCookies", () => ({
  isFlagEnabled: vi.fn(),
}));

vi.mock("~/api/notes/getClients.server", () => ({
  getClients: vi.fn(),
}));

beforeAll(() => {
  vi.mock("react-window", () => ({
    FixedSizeList: ({ children, itemCount, onItemsRendered: onItems }: any) => {
      const simulateScroll = () => {
        if (onItems) {
          onItems({
            visibleStartIndex: itemCount - 2,
            visibleStopIndex: itemCount - 1,
            overscanStartIndex: itemCount - 2,
            overscanStopIndex: itemCount - 1,
          });
        }
      };

      const items = [];
      for (let i = 0; i < itemCount; i++) {
        items.push(
          <div key={i} data-testid={`virtualized-item-${i}`}>
            {children({ index: i, style: {} })}
          </div>
        );
      }

      return (
        <div data-testid="virtualized-list">
          {items}
          <button data-testid="simulate-scroll" onClick={simulateScroll}>
            Simulate Scroll
          </button>
        </div>
      );
    },
  }));

  vi.mock("react-virtualized-auto-sizer", () => ({
    default: ({ children }: { children: any }) =>
      children({ width: 800, height: 600 }),
  }));
});

const mockClients = [
  { uuid: "client-1", name: "Client One", type: "individual" },
  { uuid: "client-2", name: "Client Two", type: "business" },
  { uuid: "client-3", name: "Client Three", type: "individual" },
];

const createMockClientListResponse = (
  clients = mockClients,
  nextPageToken = "next-page-token"
): ClientListResponse => ({
  clients,
  nextPageToken,
  clientSelectionEnabled: true,
  crmSystem: "mock-crm",
});

describe("Clients Route", () => {
  beforeEach(() => {
    vi.mocked(isFlagEnabled).mockReturnValue(true);
    vi.mocked(getClients).mockResolvedValue(
      createMockClientListResponse(mockClients, "next-page-token")
    );

    // Reset fetcher mock
    mockFetcher.data = null;
    mockFetcher.state = "idle";
    mockFetcher.load.mockClear();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the clients list correctly", async () => {
    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByTestId("virtualized-list");

    expect(screen.getByText("Client One")).toBeInTheDocument();
    expect(screen.getByText("Client Two")).toBeInTheDocument();
    expect(screen.getByText("Client Three")).toBeInTheDocument();
  });

  it("displays 'Nothing here... yet.' when no clients are found", async () => {
    vi.mocked(getClients).mockResolvedValueOnce(
      createMockClientListResponse([], "")
    );

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Nothing here... yet.");
  });

  it("allows searching for clients", async () => {
    const user = userEvent.setup();

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader: () => {
          return data({
            clients: mockClients,
            nextPageToken: "next-page-token",
            isPaginationEnabled: true,
          });
        },
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Two");

    // Verify that the fetcher.load was called with the correct URL
    await waitFor(() => {
      expect(mockFetcher.load).toHaveBeenCalledWith("/clients?searchTerm=Two");
    });

    // Verify that the search input has the correct value
    expect(searchInput).toHaveValue("Two");
  });

  it("shows loading indicator during search", async () => {
    const user = userEvent.setup();

    // Mock a delayed response to ensure the spinner is visible
    vi.mocked(getClients)
      .mockImplementationOnce(() =>
        Promise.resolve(createMockClientListResponse(mockClients))
      )
      .mockImplementationOnce(
        () =>
          new Promise((resolve) => {
            // Delay the response to ensure the spinner is visible
            setTimeout(() => {
              resolve(
                createMockClientListResponse([
                  { uuid: "client-1", name: "Client One", type: "individual" },
                ])
              );
            }, 100);
          })
      );

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Test");

    await waitFor(() => {
      const searchLoadingIcon = screen.getByTestId("search-spinner-icon");
      expect(searchLoadingIcon).toBeInTheDocument();
    });
  });

  it("allows clearing the search query", async () => {
    const user = userEvent.setup();

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Test");

    const clearButton = screen.getByTestId("clear-search-icon");
    await user.click(clearButton);

    expect(searchInput).toHaveValue("");
  });

  it("loads more clients when scrolling to the bottom", async () => {
    // Clients for first page
    const initialClients = [
      { uuid: "client-1", name: "Client One", type: "individual" },
      { uuid: "client-2", name: "Client Two", type: "business" },
    ];

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader: () => {
          return data({
            clients: initialClients,
            nextPageToken: "next-page-token",
            isPaginationEnabled: true,
          });
        },
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");
    expect(screen.getByText("Client Two")).toBeInTheDocument();

    const initialClientCards = screen.getAllByTestId("client-card-wrapper");
    expect(initialClientCards.length).toBe(2);

    const scrollButton = await screen.findByTestId("simulate-scroll");
    const user = userEvent.setup();
    await user.click(scrollButton);

    // Verify that the fetcher.load was called for pagination
    await waitFor(() => {
      expect(mockFetcher.load).toHaveBeenCalledWith(
        "/clients?cursor=next-page-token"
      );
    });
  });

  it("doesn't load more clients when cursor is empty", async () => {
    vi.mocked(getClients).mockReset();

    vi.mocked(getClients).mockResolvedValue(
      createMockClientListResponse(mockClients, "")
    );

    // Create a custom loader that we can spy on
    const customLoader = vi.fn(async () => {
      return data({
        clients: mockClients,
        nextPageToken: "",
        isPaginationEnabled: true,
        clientSelectionEnabled: true,
        crmSystem: "mock-crm",
      });
    });

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader: customLoader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");

    expect(screen.getByText("Client Two")).toBeInTheDocument();
    expect(screen.getByText("Client Three")).toBeInTheDocument();

    const initialClientCards = screen.getAllByTestId("client-card-wrapper");
    const initialCount = initialClientCards.length;

    const initialLoaderCalls = customLoader.mock.calls.length;

    // Simulate scrolling to the bottom by clicking the button we added to our mock
    const scrollButton = await screen.findByTestId("simulate-scroll");
    const user = userEvent.setup();
    await user.click(scrollButton);

    await waitFor(() => {
      const finalClientCards = screen.getAllByTestId("client-card-wrapper");
      expect(finalClientCards.length).toBe(initialCount);
    });

    expect(customLoader.mock.calls.length).toBe(initialLoaderCalls);
  });

  it("doesn't attempt pagination when the feature flag is disabled", async () => {
    vi.mocked(isFlagEnabled).mockReturnValue(false);

    const routes = [
      {
        path: "/clients",
        Component: Route,
        loader,
      },
    ];

    renderRouterWithWrapper(routes, ["/clients"], TooltipProvider);

    await screen.findByText("Client One");

    expect(getClients).toHaveBeenCalledWith(
      expect.objectContaining({
        pageSize: 0,
      })
    );
  });
});
