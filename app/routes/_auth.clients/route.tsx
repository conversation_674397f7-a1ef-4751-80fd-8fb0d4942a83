import { useState, useEffect, useRef } from "react";
import { AddOutlined } from "@mui/icons-material";
import { data, type LoaderFunctionArgs } from "react-router";
import {
  NavLink,
  Outlet,
  useFetcher,
  useLoaderData,
  useLocation,
} from "react-router";

import AutoSizer from "react-virtualized-auto-sizer";
import { FixedSizeList, ListChildComponentProps } from "react-window";

import { Typography } from "~/@ui/Typography";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Fab } from "~/@ui/Fab";
import { ClientCard } from "~/@ui/clients/ClientCard";
import { getClients } from "~/api/notes/getClients.server";
import { useDebounce } from "~/utils/useDebounce";
import { isFlagEnabled } from "~/utils/flagsInCookies";
import { Spinner } from "~/@ui/assets/Spinner";
import { X } from "lucide-react";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  const searchTerm = searchParams.get("searchTerm") ?? undefined;
  const cursor = searchParams.get("cursor") || "";

  const isPaginationEnabled = isFlagEnabled(request, "EnableClientsPagination");

  const { clients, nextPageToken } = await getClients({
    searchTerm,
    request,
    pageSize: isPaginationEnabled ? 30 : 0,
    cursor,
  });

  return data({ clients, nextPageToken, isPaginationEnabled });
};

const Route = () => {
  const { clients, nextPageToken, isPaginationEnabled } =
    useLoaderData<typeof loader>();
  const location = useLocation();
  const fetcher = useFetcher<typeof loader>();

  const [filteredClients, setFilteredClients] = useState(clients);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [cursor, setCursor] = useState(nextPageToken);
  const [isLoadingMoreRows, setIsLoadingMoreRows] = useState(false); // triggered by user scrolling to the end
  const [isLoadingSearchResults, setIsLoadingSearchResults] = useState(false); // triggered by search term changes

  const newSearch = useRef(false); // indicates whether a new search is happening

  const debouncedSearchQuery = useDebounce(searchQuery, 400);

  // re-fetch clients when search query changes
  useEffect(() => {
    const searchParams = new URLSearchParams();
    debouncedSearchQuery &&
      searchParams.set("searchTerm", debouncedSearchQuery);

    newSearch.current = true;
    setIsLoadingSearchResults(true);

    fetcher.load(`/clients?${searchParams.toString()}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchQuery]);

  // Handle fetcher data when new clients are loaded
  useEffect(() => {
    if (!fetcher.data) {
      return;
    }

    const { clients: newClients, nextPageToken: newNextPageToken } =
      fetcher.data;
    const isNewSearch = newSearch.current;
    setFilteredClients((prev) =>
      isNewSearch ? newClients : [...prev, ...newClients]
    );
    newSearch.current = false;
    setCursor(newNextPageToken);
    setIsLoadingMoreRows(false);
    setIsLoadingSearchResults(false);
  }, [fetcher.data]);

  const loadMore = () => {
    if (fetcher.state !== "idle" || !cursor) {
      return;
    }

    const searchParams = new URLSearchParams();
    searchQuery && searchParams.set("searchTerm", searchQuery);
    cursor && searchParams.set("cursor", cursor);

    fetcher.load(`/clients?${searchParams.toString()}`);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const Row = ({ index, style }: ListChildComponentProps) => {
    const client = filteredClients[index];
    if (!client) {
      return null;
    }
    return (
      <div style={style}>
        <div className="py-2" data-testid="client-card-wrapper">
          <ClientCard
            clientName={client.name}
            clientType={client.type}
            to={{ pathname: `/clients/${client.uuid}` }}
            className="w-full"
          />
        </div>
      </div>
    );
  };

  return (
    <LayoutV2>
      <ContentV2
        floatingAction={
          <Fab asChild>
            <NavLink to="/notes/create">
              <AddOutlined />
            </NavLink>
          </Fab>
        }
        className="relative w-80 min-w-80"
      >
        <div className="relative flex w-full flex-col gap-4 text-gray-500">
          <input
            type="text"
            placeholder="Search by client name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full rounded border border-gray-300 p-2 shadow-sm"
            data-testid="search-input"
          />
          <div className="pointer-events-none absolute right-2 top-1/2 flex -translate-y-1/2 transform gap-1">
            {searchQuery && (
              <button
                onClick={handleClearSearch}
                className="pointer-events-auto"
                data-testid="clear-search-icon"
              >
                <X className="!h-5 !w-5" />
              </button>
            )}
            {isLoadingSearchResults && (
              <span data-testid="search-spinner-icon">
                <Spinner />
              </span>
            )}
          </div>
        </div>

        {filteredClients.length > 0 ? (
          <AutoSizer>
            {({ height, width }) => (
              <FixedSizeList
                height={height - 38} // to avoid the extra scrollbar
                width={width}
                itemSize={94}
                itemCount={filteredClients.length}
                onItemsRendered={({
                  visibleStopIndex,
                }: {
                  visibleStopIndex: number;
                }) => {
                  if (!isPaginationEnabled) {
                    return;
                  }

                  // load more data when user scrolls near the end
                  if (
                    visibleStopIndex >= filteredClients.length - 5 &&
                    fetcher.state === "idle" &&
                    cursor
                  ) {
                    setIsLoadingMoreRows(true);
                    loadMore();
                  }
                }}
              >
                {Row}
              </FixedSizeList>
            )}
          </AutoSizer>
        ) : (
          <Typography
            className="mt-4 self-stretch text-center"
            color="secondary"
          >
            Nothing here... yet.
          </Typography>
        )}

        {isLoadingMoreRows && (
          <div className="pointer-events-none absolute bottom-0 left-4 right-4 flex h-6 items-center justify-center rounded-t-lg bg-black text-xs text-primary-foreground opacity-50">
            Loading...
          </div>
        )}
      </ContentV2>

      <Outlet context={{ clients }} key={location.pathname} />
    </LayoutV2>
  );
};

export default Route;
