import { LoaderFunctionArgs, data, redirect } from "react-router";
import { Link, useLoaderData } from "react-router";
import { addDays, format, isAfter, isBefore, parse } from "date-fns";
import { useEffect, useLayoutEffect, useMemo, useState } from "react";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Badge } from "~/@shadcn/ui/badge";
import { Typography } from "~/@ui/Typography";
import { getNotes } from "~/api/notes/getNotes.server";
import {
  AttendeeInfo,
  AttendeesApi,
  AttendeeType,
  Configuration,
  ListNotesResponse,
  ListTasksResponse,
  TaskApi,
} from "~/api/openapi/generated";
import { Bar, Line, Pie } from "react-chartjs-2";
import { ClientOnly } from "remix-utils/client-only";
import "chart.js/auto";
import { Chart } from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { configurationParameters } from "~/api/openapi/configParams";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { Button } from "~/@shadcn/ui/button";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import {
  getClientCountByOwner,
  getNotesByDate,
  getNotesByAudioSource,
  getNotesByAdvisor,
  getNotesByStatus,
  getTagCounts,
  getTagsByClient,
  getTasksByAssignee,
  getTotalDurationByAdvisorAndDay,
  Filter,
} from "./dataProcessing";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import "./insights-overrides.css";
import GridLayout from "react-grid-layout";
import { withBarColors, withLineColors, withPieColors } from "./colors";
import {
  DownloadOutlined,
  EditOutlined,
  FilterListOutlined,
  RestorePageOutlined,
  SaveOutlined,
} from "@mui/icons-material";
import { SerializeFrom } from "~/types/remix";

import { cn } from "~/@shadcn/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "~/@shadcn/ui/popover";
import { Tabs, TabsList, TabsTrigger } from "~/@shadcn/ui/tabs";
import { MultiSelect } from "~/@ui/MultiSelect";

Chart.register(ChartDataLabels);

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const notesPromise = getNotes({ request }).catch((error) => {
    if (error.response?.status === 401) {
      throw redirect("/auth/logout");
    }
    return undefined;
  });

  const user = await getUserSessionOrRedirect(request);
  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const tasksPromise = new TaskApi(configuration)
    .taskListTasks({
      userUuid: user.userId,
    })
    .catch((error) => {
      if (error.response?.status === 401) {
        throw redirect("/auth/logout");
      }
      return undefined;
    });

  const attendeesPromise = new AttendeesApi(
    configuration
  ).attendeesListAttendees();

  const [notes, tasks, attendees] = await Promise.all([
    notesPromise,
    tasksPromise,
    attendeesPromise,
  ]);

  return data({
    notes,
    tasks,
    attendees,
  });
};

// A skeleton to show before the page has loaded.
const Skeletons = () => (
  <div className="flex w-[1620px] flex-col space-y-4">
    <Skeleton className="h-16 w-full" />
    <div className="grid w-full grid-cols-3 gap-[20px]">
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="asect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
      <Skeleton className="aspect-[2/1] h-full w-full" />
    </div>
  </div>
);

const ChartTitle = ({
  title,
  downloadEnabled,
  onDownload,
}: {
  title: string;
  downloadEnabled: boolean;
  onDownload: () => void;
}) => {
  return (
    <div className="flex flex-row items-center justify-between">
      <Typography className="tracking-wide" variant="h4">
        {title}
      </Typography>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={onDownload}
              disabled={!downloadEnabled}
            >
              <DownloadOutlined />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Download data</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

// Displays an editable grid of charts/dashboards.
const Charts = ({
  notes,
  tasks,
  attendees,
  context,
}: {
  notes: ListNotesResponse[];
  tasks: SerializeFrom<ListTasksResponse>;
  attendees: AttendeeInfo[];
  context: "practice" | "client";
}) => {
  const today = new Date();
  const oneYearAgo = addDays(today, -365);
  const [endDate, setEndDate] = useState(today);
  const [startDate, setStartDate] = useState(addDays(today, -7));

  const [isStatic, setIsStatic] = useState(true);

  const defaultSize = useMemo(() => ({ w: 6, h: 3 }), []);
  const defaultLayouts: Record<Tab, GridLayout.Layout[]> = useMemo(
    () => ({
      practice: [
        { x: 6 * 0, y: 3 * 0, ...defaultSize, i: "meetings-per-day" },
        { x: 6 * 1, y: 3 * 0, ...defaultSize, i: "meetings-using-zeplyn" },
        { x: 6 * 2, y: 3 * 0, ...defaultSize, i: "meeting-method" },
        { x: 6 * 0, y: 3 * 1, ...defaultSize, i: "meeting-outcomes" },
        { x: 6 * 1, y: 3 * 1, ...defaultSize, i: "tasks-assigned" },
        { x: 6 * 2, y: 3 * 1, ...defaultSize, i: "zeplin-usage-by-advisor" },
      ],
      client: [
        { x: 9 * 0, y: 0 * 3, w: 9, h: 3, i: "topics", isResizable: false },
        {
          x: 9 * 1,
          y: 0 * 3,
          w: 9,
          h: 3,
          i: "client-topics",
          isResizable: false,
        },
        { x: 6 * 0, y: 3 * 1, ...defaultSize, i: "client-count" },
      ],
    }),
    [defaultSize]
  );
  const [layouts, setLayouts] = useState(defaultLayouts);

  // Load the layout from local storage on component mount (if it has been stored).
  useLayoutEffect(() => {
    const layouts = window.localStorage.getItem("layouts");
    setLayouts(layouts ? JSON.parse(layouts) : defaultLayouts);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Save the layout into local storage when it changes.
  useEffect(() => {
    window.localStorage.setItem("layouts", JSON.stringify(layouts));
  }, [layouts]);

  const layout = useMemo(() => layouts[context], [layouts, context]);
  const activeCharts = new Set(layout.map((item) => item.i));

  // Given a layout for a grid, updates the `static` property of each item (i.e., makes the grid
  // un-editable).
  const updateLayoutWithStatic = (
    layout: GridLayout.Layout[],
    isStatic: boolean
  ) => layout.map((item) => ({ ...item, static: isStatic }));

  const attendeeUUIDToClientUUID = attendees.reduce(
    (acc, attendee) => {
      if (attendee.clientUuid) {
        acc[attendee.uuid] = attendee.clientUuid;
      }
      return acc;
    },
    {} as Record<string, string>
  );

  const attendeeUUIDToUserUUID = attendees.reduce(
    (acc, attendee) => {
      if (attendee.userUuid) {
        acc[attendee.uuid] = attendee.userUuid;
      }
      return acc;
    },
    {} as Record<string, string>
  );

  const userUUIDToUserName = attendees.reduce(
    (acc, attendee) => {
      if (attendee.type === AttendeeType.User && attendee.userUuid) {
        acc[attendee.userUuid] = attendee.name;
      }
      return acc;
    },
    {} as Record<string, string>
  );

  const [selectedAdvisors, setSelectedAdvisors] = useState<string[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);

  // Memoize the data processing functions to avoid re-computing them on every render.
  const filter: Filter = useMemo(
    () => ({
      startDate,
      endDate,
      advisorUUIDs: selectedAdvisors,
      clientUUIDs: selectedClients,
    }),
    [startDate, endDate, selectedAdvisors, selectedClients]
  );

  const countsByTag = useMemo(
    () =>
      getTagCounts(
        notes,
        attendeeUUIDToClientUUID,
        attendeeUUIDToUserUUID,
        filter
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const tagsByClient = useMemo(
    () =>
      getTagsByClient(
        notes,
        attendeeUUIDToClientUUID,
        attendeeUUIDToUserUUID,
        filter
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const notesByDate = useMemo(
    () =>
      withBarColors(
        getNotesByDate(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          filter
        )
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const notesByOwner = useMemo(
    () =>
      withLineColors(
        getNotesByAdvisor(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          userUUIDToUserName,
          filter
        )
      ),
    [
      notes,
      attendeeUUIDToClientUUID,
      attendeeUUIDToUserUUID,
      filter,
      userUUIDToUserName,
    ]
  );
  const notesByAudioSource = useMemo(
    () =>
      withPieColors(
        getNotesByAudioSource(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          filter
        )
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const notesByStatus = useMemo(
    () =>
      withPieColors(
        getNotesByStatus(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          filter
        )
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const tasksByAssignee = useMemo(
    () => withBarColors(getTasksByAssignee(tasks, filter)),
    [tasks, filter]
  );
  const durationByAdvisorAndDay = useMemo(
    () =>
      withLineColors(
        getTotalDurationByAdvisorAndDay(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          filter
        )
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );
  const clientCountByOwner = useMemo(
    () =>
      withBarColors(
        getClientCountByOwner(
          notes,
          attendeeUUIDToClientUUID,
          attendeeUUIDToUserUUID,
          filter
        )
      ),
    [notes, attendeeUUIDToClientUUID, attendeeUUIDToUserUUID, filter]
  );

  const downloadCSVData = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Returns a constructed value for a `layout` for a Chart.js chart's options.
  const makeLayout = (rightPadding: number = 0) => {
    return {
      padding: {
        top: 30,
        right: rightPadding,
      },
    };
  };

  /// Returns a constructed value for a Chart.js chart options' `elements` for a bar graph.
  const makeBarElements = () => ({
    bar: { borderRadius: 4 },
  });

  /// Returns a constructed value for a Chart.js chart options' `scales`.
  const makeScales = ({
    showXTicks = true,
    showYGrid = false,
    showYTicks = false,
  } = {}) => {
    return {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          display: showXTicks,
        },
      },
      y: {
        grid: {
          display: showYGrid,
        },
        ticks: {
          display: showYTicks,
          precision: 0,
        },
      },
    };
  };

  // Fills an array with a given item to make its length a multiple of a given length.
  function fillToMultipleOfLength<T>(array: T[], length: number, item: T) {
    if (array.length % length === 0) {
      return array;
    }
    const fillLength = length - (array.length % length);
    return [...array, ...new Array(fillLength).fill(item)];
  }

  return (
    <div className="h-full w-full overflow-x-auto overflow-y-visible">
      {/* Filter and layout controls */}
      <div className="flex w-full flex-col items-center justify-start gap-4 md:flex-row">
        {/* Filter popover */}
        <div className="flex flex-col items-center gap-2 md:flex-row">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <FilterListOutlined fontSize="small" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto" side="right">
              {/* Date range */}
              <Typography variant="h5">Date range</Typography>
              <div className="flex flex-row items-center gap-2">
                <input
                  type="date"
                  placeholder="Start date"
                  value={format(startDate, "yyyy-MM-dd")}
                  onChange={(e) => {
                    const newStartDate = parse(
                      e.target.value,
                      "yyyy-MM-dd",
                      new Date()
                    );
                    if (isNaN(newStartDate.getTime())) {
                      return;
                    }
                    if (isAfter(newStartDate, today)) {
                      return;
                    }
                    if (isAfter(newStartDate, endDate)) {
                      setEndDate(newStartDate);
                    }
                    setStartDate(newStartDate);
                  }}
                  max={format(today, "yyyy-MM-dd")}
                  min={format(oneYearAgo, "yyyy-MM-dd")}
                  className="rounded-md border border-gray-200 p-2"
                />
                <Typography variant="h5">-</Typography>
                <input
                  type="date"
                  placeholder="End date"
                  value={format(endDate, "yyyy-MM-dd")}
                  onChange={(e) => {
                    const newEndDate = parse(
                      e.target.value,
                      "yyyy-MM-dd",
                      new Date()
                    );
                    if (isNaN(newEndDate.getTime())) {
                      return;
                    }
                    if (isAfter(newEndDate, today)) {
                      return;
                    }
                    if (isBefore(newEndDate, startDate)) {
                      setStartDate(newEndDate);
                    }
                    setEndDate(newEndDate);
                  }}
                  max={format(today, "yyyy-MM-dd")}
                  min={format(oneYearAgo, "yyyy-MM-dd")}
                  className="rounded-md border border-gray-200 p-2"
                />
              </div>

              {/* Filter by advisor */}
              <div className="flex flex-col gap-2">
                <Typography variant="h5">Advisor</Typography>
                <MultiSelect
                  placeholder="Advisor"
                  options={Object.entries(
                    attendees.reduce(
                      (acc, a) => {
                        if (a.type === AttendeeType.User && a.userUuid) {
                          acc[a.userUuid] = a.name;
                        }
                        return acc;
                      },
                      {} as Record<string, string>
                    )
                  )
                    .sort(([_, nameOne], [__, nameTwo]) =>
                      nameOne.localeCompare(nameTwo)
                    )
                    .map(([uuid, name]) => ({ value: uuid, label: name }))}
                  selected={selectedAdvisors}
                  onChange={(advisors) => {
                    setSelectedAdvisors(advisors);
                  }}
                />
              </div>

              {/* Filter by client */}
              <div className="flex flex-col gap-2">
                <Typography variant="h5">Client</Typography>
                <MultiSelect
                  placeholder="Client"
                  options={Object.entries({
                    // Add clients from notes and attendees
                    ...notes.reduce(
                      (acc, n) => {
                        if (n.client && n.client.name) {
                          acc[n.client.uuid] = n.client.name;
                        }
                        return acc;
                      },
                      {} as Record<string, string>
                    ),
                    ...attendees.reduce(
                      (acc, a) => {
                        if (a.type === AttendeeType.Client && a.clientUuid) {
                          acc[a.clientUuid] = a.name;
                        }
                        return acc;
                      },
                      {} as Record<string, string>
                    ),
                  })
                    .sort(
                      ([_, nameOne], [__, nameTwo]) =>
                        nameOne.localeCompare(nameTwo) // sort by client name
                    )
                    .map(([uuid, name]) => ({ value: uuid, label: name }))}
                  selected={selectedClients}
                  onChange={(clients) => {
                    setSelectedClients(clients);
                  }}
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Layout controls */}
        <div className="flex gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setIsStatic(!isStatic);
            }}
          >
            {isStatic ? <EditOutlined /> : <SaveOutlined />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setLayouts({ ...layouts, [context]: defaultLayouts[context] });
            }}
            disabled={!isStatic}
          >
            <RestorePageOutlined />
          </Button>
        </div>
      </div>

      {/* Charts */}
      <GridLayout
        autoSize={true}
        cols={18}
        rowHeight={1620 / 18}
        width={1620}
        compactType="horizontal"
        margin={[20, 20]}
        layout={updateLayoutWithStatic(layout, isStatic)}
        onLayoutChange={(layout) =>
          setLayouts({ ...layouts, [context]: layout })
        }
        onResize={(_, __, layoutItem, placeholder) => {
          if (layoutItem.w < 3) {
            layoutItem.w = 3;
            placeholder.w = 3;
          }
          if (layoutItem.w / layoutItem.h !== 2) {
            layoutItem.h = layoutItem.w / 2;
            placeholder.h = layoutItem.w / 2;
          }
        }}
      >
        {/* Meetings per day */}
        {activeCharts.has("meetings-per-day") && (
          <div key="meetings-per-day">
            <ChartTitle
              title="MEETINGS PER DAY"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Date", "Count"],
                  ...(notesByDate.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    notesByDate.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meetings_per_day.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={notesByDate}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                layout: makeLayout(),
                elements: makeBarElements(),
                scales: makeScales(),
              }}
            />
          </div>
        )}

        {/* Meetings using Zeplyn by advisor */}
        {activeCharts.has("meetings-using-zeplyn") && (
          <div key="meetings-using-zeplyn">
            <ChartTitle
              title="MEETINGS USING ZEPYLN BY ADVISOR"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Date", "Advisor", "Count"],
                  ...(notesByOwner.labels ?? []).flatMap((label, index) =>
                    notesByOwner.datasets.map((dataset) => [
                      `"${label}"`,
                      `"${dataset.label}"`,
                      dataset.data[index],
                    ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(
                  csvContent,
                  "meetings_using_zeplyn_by_advisor.csv"
                );
              }}
            />
            <Line
              className={"self-center"}
              data={notesByOwner}
              options={{
                plugins: {
                  legend: {
                    position: "bottom",
                    fullSize: false,
                    labels: {
                      boxWidth: 12,
                      padding: 8,
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                scales: makeScales({ showYGrid: true, showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Meeting method */}
        {activeCharts.has("meeting-method") && (
          <div key="meeting-method">
            <ChartTitle
              title="MEETING METHOD"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Method", "Count"],
                  ...(notesByAudioSource.labels ?? []).map((label, index) => [
                    label,
                    notesByAudioSource.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meeting_method.csv");
              }}
            />
            <Pie
              data={notesByAudioSource}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Meeting outcomes */}
        {activeCharts.has("meeting-outcomes") && (
          <div key="meeting-outcomes">
            <ChartTitle
              title="MEETING OUTCOMES"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Outcome", "Count"],
                  ...(notesByStatus.labels ?? []).map((label, index) => [
                    label,
                    notesByStatus.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "meeting_outcomes.csv");
              }}
            />
            <Pie
              data={notesByStatus}
              className={"self-center"}
              options={{
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      usePointStyle: true,
                      pointStyle: "circle",
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                aspectRatio: 2,
              }}
            />
          </div>
        )}

        {/* Tasks assigned */}
        {activeCharts.has("tasks-assigned") && (
          <div key="tasks-assigned">
            <ChartTitle
              title="TASKS ASSIGNED"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Assignee", "Count"],
                  ...(tasksByAssignee.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    tasksByAssignee.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "tasks_assigned.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={tasksByAssignee}
              options={{
                indexAxis: "y",
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "right",
                    anchor: "end",
                  },
                },
                layout: makeLayout(40),
                elements: makeBarElements(),
                scales: makeScales({ showXTicks: false }),
              }}
            />
          </div>
        )}

        {/* Zeplyn usage (time) by advisor */}
        {activeCharts.has("zeplin-usage-by-advisor") && (
          <div key="zeplin-usage-by-advisor">
            <ChartTitle
              title="ZEPLYN USAGE BY ADVISOR"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Date", "Advisor", "Minutes"],
                  ...(durationByAdvisorAndDay.labels ?? []).flatMap(
                    (label, index) =>
                      durationByAdvisorAndDay.datasets.map((dataset) => [
                        `"${label}"`,
                        `"${dataset.label}"`,
                        dataset.data[index],
                      ])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "zeplin_usage_by_advisor.csv");
              }}
            />
            <Line
              className={"self-center"}
              data={durationByAdvisorAndDay}
              options={{
                plugins: {
                  legend: {
                    position: "bottom",
                    fullSize: false,
                    labels: {
                      boxWidth: 12,
                      padding: 8,
                    },
                  },
                  datalabels: {
                    display: false,
                  },
                },
                layout: makeLayout(),
                scales: makeScales({ showYGrid: true, showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Client count */}
        {activeCharts.has("client-count") && (
          <div key="client-count">
            <ChartTitle
              title="NUMBER OF CLIENTS MET WITH"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Advisor", "Client count"],
                  ...(clientCountByOwner.labels ?? []).map((label, index) => [
                    `"${label}"`,
                    clientCountByOwner.datasets[0]?.data[index],
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "client_count.csv");
              }}
            />
            <Bar
              className={"self-center"}
              data={clientCountByOwner}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                  datalabels: {
                    display: true,
                    align: "top",
                    anchor: "end",
                  },
                },
                elements: makeBarElements(),
                scales: makeScales({ showYTicks: true }),
              }}
            />
          </div>
        )}

        {/* Topics */}
        {activeCharts.has("topics") && (
          <div key="topics">
            <ChartTitle
              title="TOPICS"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Topic", "Count"],
                  ...Object.entries(countsByTag).map(([tag, count]) => [
                    `"${tag}"`,
                    count,
                  ]),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "topics.csv");
              }}
            />
            <div
              className={cn(
                "m-1 mr-4 grid grid-flow-col grid-rows-6 gap-x-[1px] bg-border",
                isStatic ? "" : "pointer-events-none"
              )}
            >
              {fillToMultipleOfLength(
                Object.entries(countsByTag)
                  .sort(([_, countOne], [__, countTwo]) => countTwo - countOne)
                  .slice(0, 18),
                6,
                ["", 0]
              ).map(([tag, count], i) => (
                <div
                  key={count > 0 ? tag : i}
                  className={cn(
                    "flex flex-row items-center justify-between bg-background py-1",
                    i < 6 ? "pr-6" : i < 12 ? "pl-6 pr-6" : "pl-6"
                  )}
                >
                  {count > 0 && (
                    <>
                      <Typography
                        variant="default"
                        className="text-sky-700"
                        asChild
                      >
                        <Link
                          className="line-clamp-1 underline"
                          to={`/notes?searchTerm=${encodeURIComponent(tag)}`}
                        >
                          {tag}
                        </Link>
                      </Typography>
                      <Typography variant="default" asChild>
                        <span className="ml-8">{count}</span>
                      </Typography>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Client Topics */}
        {activeCharts.has("client-topics") && (
          <div key="client-topics">
            <ChartTitle
              title="CLIENT TOPICS"
              downloadEnabled={isStatic}
              onDownload={() => {
                const csvContent = [
                  ["Client", "Topic (in order of frequency)"],
                  ...Object.entries(tagsByClient).flatMap(
                    ([_, { name, tags }]) =>
                      tags.map((tag) => [`"${name}"`, `"${tag}"`])
                  ),
                ]
                  .map((e) => e.join(","))
                  .join("\n");

                downloadCSVData(csvContent, "client_topics.csv");
              }}
            />
            <div
              className={cn(
                "grid-col m-1 grid grid-cols-5 gap-x-8",
                isStatic ? "" : "pointer-events-none"
              )}
            >
              {Object.entries(tagsByClient).filter(
                ([_, { tags }]) => tags.length > 0
              ).length === 0 && (
                <Typography variant="default" className="col-span-5">
                  No tags available for any clients.
                </Typography>
              )}

              {Object.entries(tagsByClient)
                .sort(
                  ([_, clientTagsOne], [__, clientTagsTwo]) =>
                    clientTagsTwo.tags.length - clientTagsOne.tags.length
                )
                .filter(([_, { tags }]) => tags.length > 0)
                .slice(0, 5)
                .map(([uuid, { name, tags }]) => (
                  <div
                    key={uuid}
                    className="flex flex-col items-center gap-y-2"
                  >
                    <Typography variant="default" asChild>
                      <Link
                        className="pb-2 text-sky-700 underline"
                        to={`/clients/${uuid}`}
                      >
                        {name}
                      </Link>
                    </Typography>
                    {tags.slice(0, 3).map((tag) => (
                      <Badge
                        key={`${uuid}-${tag}`}
                        variant="slate"
                        className="line-clamp-3 w-full justify-center text-center"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                ))}
            </div>
          </div>
        )}
      </GridLayout>
    </div>
  );
};

// Tabs that can be selected on the dashboard.
type Tab = "client" | "practice";

const tabs = [
  {
    label: "Client metrics",
    value: "client",
  },
  {
    label: "Practice metrics",
    value: "practice",
  },
];

const TabbedCharts = ({
  notes,
  tasks,
  attendees,
}: {
  notes: ListNotesResponse[];
  tasks: SerializeFrom<ListTasksResponse>;
  attendees: AttendeeInfo[];
}) => {
  const [selectedTab, setSelectedTab] = useState<Tab>("client");

  return (
    <div className="w-full">
      <Tabs
        value={selectedTab}
        onValueChange={(newValue) => {
          setSelectedTab(newValue as Tab);
        }}
        className="self-start"
      >
        <TabsList>
          {tabs.map(({ label, value }) => (
            <TabsTrigger key={value} value={value}>
              {label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
      <Charts
        notes={notes}
        tasks={tasks}
        attendees={attendees}
        context={selectedTab}
      />
    </div>
  );
};

// Displays a grid of charts and tables with information about the user's (and/or user's
// organization's, depending on the user's privilege level) Zeplyn usage.
const InsightsDashboard = () => {
  const { notes, tasks, attendees } = useLoaderData<typeof loader>();

  return (
    <LayoutV2>
      <ContentV2>
        {notes === undefined || tasks === undefined ? (
          <Typography variant="h4" color="error">
            Could not load dashboard.
          </Typography>
        ) : (
          <ClientOnly>
            {() => (
              <TabbedCharts notes={notes} tasks={tasks} attendees={attendees} />
            )}
          </ClientOnly>
        )}
      </ContentV2>
    </LayoutV2>
  );
};
export default InsightsDashboard;
