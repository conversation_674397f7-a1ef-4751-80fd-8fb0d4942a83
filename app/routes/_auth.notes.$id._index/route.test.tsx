import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Route from "./route";
import { data } from "react-router";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";
import { mapNoteToReducerState } from "./editableNoteReducer";
import {
  MeetingCategory,
  NoteResponse,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { ActionFunctionArgs } from "react-router";
import { SerializeFrom } from "~/types/remix";
import { LayoutV2 } from "~/@ui/layout/LayoutV2";
import { renderRouter } from "~/utils/testUtils";

describe("route", () => {
  it("should sort follow-up tabs correctly", async () => {
    const user = userEvent.setup();
    const note: SerializeFrom<NoteResponse> = {
      attendees: [],
      authorizedUserUuids: [],
      created: new Date(),
      followUps: [],
      isDeleted: false,
      tags: [],
      transcript: { utterances: [] },
      uuid: "note-uuid",
      meetingCategory: MeetingCategory.Client,
      meetingDurationSeconds: 3600,
      meetingName: "Test meeting",
      meetingTypeUuid: "uuid",
      modified: new Date(),
      noteType: NoteType.MeetingRecording,
      status: ProcessingStatus.Processed,
      timesEditable: true,
    };
    const Empty = () => <div></div>;

    const WrappedRoute = () => (
      <TooltipProvider>
        <LayoutV2>
          <Route />
        </LayoutV2>
      </TooltipProvider>
    );
    const routes = [
      {
        path: "/",
        Component: Empty,
      },
      {
        path: "/notes/note-uuid",
        Component: WrappedRoute,
        action({ request }: ActionFunctionArgs) {
          return null;
        },
        loader() {
          return data({
            note: note,
            clients: [],
            initialAttendees: [],
            attendeeOptions: [],
            reducerInitialState: mapNoteToReducerState(note, []),
            userOptions: [],
            userAsAttendee: null,
            isClientSelectionEnabled: false,
            parsedFollowUps: [
              {
                followUp: {
                  uuid: "2",
                  title: "B Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: { review_entries: [] },
              },
              {
                followUp: {
                  uuid: "3",
                  title: "A Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: {
                  review_entries: [{ id: "1", kind: "header", topic: "A two" }],
                },
              },
              {
                followUp: {
                  uuid: "1",
                  title: "A Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: {
                  review_entries: [{ id: "1", kind: "header", topic: "A one" }],
                },
              },
            ],
          });
        },
      },
    ];

    renderRouter(routes, ["/notes/note-uuid"]);

    const followUpTabs = await screen.findAllByText(/Follow-up/);
    expect(followUpTabs[0]).toHaveTextContent("Preview Follow-up");
    expect(followUpTabs[1]).toHaveTextContent("A Follow-up");
    expect(followUpTabs[2]).toHaveTextContent("A Follow-up");
    expect(followUpTabs[3]).toHaveTextContent("B Follow-up");

    // Confirm the order of the tabs with the same name by checking which contents are displayed.
    await user.click(followUpTabs[1]!);
    expect(
      screen.queryByText("A one")?.closest(".hidden")
    ).not.toBeInTheDocument();
    expect(screen.getByText("A two").closest(".hidden")).toBeInTheDocument();

    await user.click(followUpTabs[2]!);
    expect(screen.getByText("A one").closest(".hidden")).toBeInTheDocument();
    expect(
      screen.queryByText("A two")?.closest(".hidden")
    ).not.toBeInTheDocument();
  });
});
