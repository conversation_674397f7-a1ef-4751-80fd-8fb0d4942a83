import { ShareRounded } from "@mui/icons-material";
import { useNavigation } from "react-router";
import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Dialog,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogContent,
  DialogDescription,
} from "~/@shadcn/ui/dialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Spinner } from "~/@ui/assets/Spinner";
import { AttendeesMultiSelect } from "~/@ui/attendees/AttendeeMultiSelect";
import { Typography } from "~/@ui/Typography";
import { AttendeeOptions } from "~/api/attendees/types";

// A button that displays a share icon.
export const ShareButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="ghost" size="icon-sm" onClick={onClick}>
          <ShareRounded className="h-5 w-5" />
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom">Share note</TooltipContent>
    </Tooltip>
  );
};

type ShareModalProps = {
  isOpen: boolean;
  userOptions: AttendeeOptions;
  selected: AttendeeOptions;
  onChangeUsers: React.Dispatch<React.SetStateAction<AttendeeOptions>>;
  onSave: () => void;
  onClose: () => void;
  errorMessage: string | null;
};

// A modal that allows the user to share a note with other users.
export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  userOptions,
  selected,
  onChangeUsers,
  onSave,
  onClose,
  errorMessage,
}) => {
  const navigation = useNavigation();
  const isSubmitting =
    (navigation.state === "submitting" || navigation.state === "loading") &&
    navigation.formMethod;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Share notes</DialogTitle>
          <DialogDescription>
            <AttendeesMultiSelect
              initialOptions={userOptions}
              selected={selected}
              emptyLabel={"Select users to share the note with"}
              onChange={onChangeUsers}
              allowNew={false}
              modal={true}
            />
            {errorMessage && (
              <Typography className="text-sm text-red-500">
                {errorMessage}
              </Typography>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onSave}>
            Save
            {isSubmitting && <Spinner className="ml-2" />}
          </Button>
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
