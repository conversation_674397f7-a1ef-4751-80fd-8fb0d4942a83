import React, { useEffect, useState } from "react";
import { ContentCopy } from "@mui/icons-material";
import { useFetcher } from "react-router";
import { CopyIcon } from "lucide-react";
import { toast } from "react-toastify";

import { copyToClipboard } from "~/utils/copyToClipboard";

// Type definitions for schema
type TableColumn = {
  id: string;
  caption: string;
  data_type: string;
};

type TableDataItem = {
  values: Record<string, any>;
  format: {
    decimal_places?: number;
    show_symbol?: boolean;
    date_format?: string;
    value_type: "number" | "percentage" | "currency" | "date";
  };
  evidence?: { quote: string; timestamp?: string }[];
};

type NumericalData = {
  table_definition: {
    title?: string;
    description?: string;
    columns: TableColumn[];
  };
  data: TableDataItem[];
};

type NumericalDataTabProps = {
  data: NumericalData;
  isEditMode: boolean;
  noteUUID: string;
  followUpUUID: string;
};

export const NumericalDataTab = ({
  data,
  data: { table_definition, data: rows },
  isEditMode,
  noteUUID,
  followUpUUID,
}: NumericalDataTabProps) => {
  const fetcher = useFetcher();
  const [editedValues, setEditedValues] = useState(
    rows.map(({ values }) => values)
  );
  const [hasChanges, setHasChanges] = useState(false);

  // check if there is something to save (`hasChanges` is true) when isEditMode turns false from true
  useEffect(() => {
    if (isEditMode || !hasChanges) {
      return;
    }

    fetcher.submit(
      {
        followUpId: followUpUUID,
        followUpData: JSON.stringify(modifyNumericalData(data, editedValues)),
        actionType: "update-meeting-follow-up",
      },
      {
        method: "post",
        action: `/notes/${noteUUID}`,
        encType: "multipart/form-data",
      }
    );
    setHasChanges(false);
  }, [
    isEditMode,
    hasChanges,
    followUpUUID,
    data,
    editedValues,
    fetcher,
    noteUUID,
  ]);

  const formatValue = (value: any, format: TableDataItem["format"]) => {
    if (value === null || value === undefined) return "-";

    const numericValue = typeof value === "number" ? value : Number(value);
    const isNumeric = !isNaN(numericValue);

    const getLocaleOptions = () => {
      const options: Intl.NumberFormatOptions = {};
      if (format.decimal_places !== undefined) {
        options.minimumFractionDigits = format.decimal_places;
        options.maximumFractionDigits = format.decimal_places;
      }
      return options;
    };

    switch (format.value_type) {
      case "number":
        return isNumeric
          ? numericValue.toLocaleString("en-US", getLocaleOptions())
          : value;
      case "percentage":
        return isNumeric
          ? `${numericValue.toLocaleString("en-US", getLocaleOptions())}%`
          : value;
      case "currency":
        if (isNumeric) {
          const formattedNumber = numericValue.toLocaleString(
            "en-US",
            getLocaleOptions()
          );
          return format.show_symbol ? `$${formattedNumber}` : formattedNumber;
        }
        return value;
      case "date":
        try {
          const dateStyleMap: Record<
            string,
            Intl.DateTimeFormatOptions["dateStyle"]
          > = {
            full: "full",
            long: "long",
            medium: "medium",
            short: "short",
          };
          const dateStyle =
            dateStyleMap[format.date_format || "medium"] || "medium";
          return new Date(value).toLocaleDateString("en-US", { dateStyle });
        } catch (error) {
          return value;
        }
      default:
        return value;
    }
  };

  const copyEntireSection = () => {
    const text = [
      "Numerical Data",
      table_definition.title,
      table_definition.description,
      table_definition.columns.map((col) => col.caption).join("\t"),
      ...editedValues.map((row) => {
        return `${table_definition.columns
          .map((col) => row[col.id])
          .join("\t")}`;
      }),
    ].join("\n");
    copyToClipboard(text, "Numerical Data");
  };

  const copySingleNumericValue = (
    value: any,
    format: TableDataItem["format"]
  ) => {
    const formattedValue = formatValue(value, format);
    navigator.clipboard.writeText(formattedValue).then(() => {
      toast.success(`Data copied successfully!`, {
        position: "top-right",
        autoClose: 2000,
      });
    });
  };

  const handleEdit = (rowIndex: number, colId: string, value: string) => {
    setEditedValues((prevRows) => {
      const newRows = [...prevRows];
      if (newRows[rowIndex]) {
        newRows[rowIndex]![colId] = value;
      }
      return newRows;
    });
    setHasChanges(true);
  };

  return (
    <div className="overflow-x-auto">
      <h2 className="flex text-xl font-bold">
        {table_definition.title}
        <CopyIcon
          className="ml-2 cursor-pointer text-gray-500 hover:text-black"
          onClick={copyEntireSection}
        />
      </h2>
      <p className="text-gray-600">{table_definition.description}</p>
      <table className="mt-4 min-w-full border-collapse border border-gray-300">
        <thead>
          <tr>
            {table_definition.columns.map((col) => (
              <th
                key={col.id}
                className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-700"
              >
                {col.caption}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => {
            const rowValues = editedValues[rowIndex];
            if (!rowValues) {
              return null;
            }
            return (
              <tr key={rowIndex} className="border-t">
                {table_definition.columns.map((col) => (
                  <td
                    key={col.id}
                    className="border border-gray-300 px-4 py-2 text-gray-700"
                  >
                    {!isEditMode &&
                      (col.data_type === "number" ? (
                        <div className="flex items-center justify-end space-x-1">
                          <span>
                            {formatValue(rowValues[col.id], row.format)}
                          </span>
                          <button
                            onClick={() =>
                              copySingleNumericValue(
                                rowValues[col.id],
                                row.format
                              )
                            }
                            className="p-1 text-blue-500 hover:text-blue-700 focus:outline-none"
                            title="Copy value"
                          >
                            <ContentCopy fontSize="small" />
                          </button>
                        </div>
                      ) : (
                        formatValue(rowValues[col.id], row.format)
                      ))}

                    {isEditMode && (
                      <input
                        type="text"
                        value={rowValues[col.id]}
                        onChange={(e) =>
                          handleEdit(rowIndex, col.id, e.target.value)
                        }
                        className="h-full w-full border-b focus:outline-none"
                      />
                    )}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

function modifyNumericalData(
  data: NumericalData,
  modifiedRows: Record<string, any>[]
) {
  return {
    ...data,
    data: data.data.map((row, index) => ({
      ...row,
      values: modifiedRows[index],
    })),
  };
}
