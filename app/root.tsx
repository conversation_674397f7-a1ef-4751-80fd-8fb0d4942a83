import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";
import type { LinksFunction, LoaderFunctionArgs } from "react-router";
import { Outlet, useLoaderData } from "react-router";
import { HtmlBody } from "~/@ui/layout/HtmlBody";
import tailwindStyles from "./tailwind.css?url";
import "@fontsource-variable/inter/index.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "intro.js/introjs.css";
import { cookieHeadersForFlags } from "~/utils/flagsInCookies";
import { data } from "./utils/reactRouter";

// Whether Datadog has been initialized on the frontend.
let datadogInitialized = false;

// Whether Datadog initialization failure has been logged.
//
// The initialization failure is logged only once to avoid spamming the logs.
let datadogInitializationFailureLogged = false;

// Initalizes Datadog logging on the frontend.
async function initializeDatadogForFrontend(
  applicationID: string | undefined,
  clientToken: string | undefined,
  environment: string | undefined,
  appVersion: string | undefined,
  releaseVersion: string | undefined,
  host: string | undefined
) {
  if (datadogInitialized) {
    return;
  }

  if (typeof window === "undefined") {
    if (datadogInitializationFailureLogged) {
      return;
    }
    datadogInitializationFailureLogged = true;

    // eslint-disable-next-line no-console
    console.info("Not initializing Datadog; running on backend.");

    return;
  }

  if (!applicationID || !clientToken || !environment || !host) {
    if (datadogInitializationFailureLogged) {
      return;
    }
    datadogInitializationFailureLogged = true;
    // eslint-disable-next-line no-console
    console.log("Datadog not initialized. Missing environment variables.");
    return;
  }

  // eslint-disable-next-line no-console
  console.log("Initializing Datadog RUM");
  datadogInitialized = true;

  const sharedParams = {
    applicationId: applicationID,
    clientToken: clientToken,
    site: "us5.datadoghq.com" as const,
    env: environment,
    version: appVersion ?? "unknown",
    sessionSampleRate: 100,
  };
  datadogRum.init({
    ...sharedParams,
    service: "frontend",
    sessionReplaySampleRate: 100,
    defaultPrivacyLevel: "mask-user-input",
    enableExperimentalFeatures: ["feature_flags"],
    workerUrl: "/datadog-worker-6_0_0.js",
    allowedTracingUrls: [
      {
        // As an appoximation, assume that requests to the Zeplyn app host with `_data=` should be
        // traced (since these are Remix loader and action URLs).
        match: (url: string) => url.startsWith(host) && url.includes("_data="),
        propagatorTypes: ["tracecontext"],
      },
    ],
  });
  datadogRum.setGlobalContextProperty(
    "release_version",
    releaseVersion ?? "unknown"
  );

  datadogLogs.init({
    ...sharedParams,
    service: "frontend-browser",
    forwardConsoleLogs: "all",
  });
  datadogLogs.setGlobalContextProperty(
    "release_version",
    releaseVersion ?? "unknown"
  );
}

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: tailwindStyles },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  return data(
    {
      environment: process.env.ZEPLYN_ENV,
      datadogApplicationID: process.env.ZEPLYN_DATADOG_APPLICATION_ID,
      datadogClientToken: process.env.ZEPLYN_DATADOG_CLIENT_TOKEN,
      appVersion: process.env.ZEPLYN_APP_VERSION,
      releaseVersion: process.env.ZEPLYN_RELEASE_VERSION,
      host: process.env.ZEPLYN_HOST,
    },
    {
      headers: await cookieHeadersForFlags(request),
    }
  );
};

const Root = () => {
  const data = useLoaderData();
  initializeDatadogForFrontend(
    data.datadogApplicationID,
    data.datadogClientToken,
    data.environment,
    data.appVersion,
    data.releaseVersion,
    data.host
  );

  return (
    <HtmlBody>
      <ToastContainer />
      <Outlet />
    </HtmlBody>
  );
};

export { ErrorBoundary } from "~/@ui/layout/ErrorBoundary";
export default Root;
